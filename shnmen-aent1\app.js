import { Analyst } from './agents/analyst.js';
import { Creator } from './agents/creator.js';
import { Critic } from './agents/critic.js';

const AGENTS = {
    analyst: Analyst,
    creator: Creator,
    critic: Critic
};

class AIInterface {
    constructor() {
        this.agentSelect = document.getElementById('agentSelect');
        this.promptInput = document.getElementById('promptInput');
        this.generateBtn = document.getElementById('generateBtn');
        this.responseContainer = document.getElementById('responseContainer');
        this.toast = document.getElementById('toast');
        this.history = [];
        
        this.initializeEventListeners();
        this.loadPreferences();
    }

    initializeEventListeners() {
        this.generateBtn.addEventListener('click', () => this.handleGeneration());
        this.promptInput.addEventListener('input', this.debounce(this.validateMentions.bind(this), 300));
        this.agentSelect.addEventListener('change', () => this.savePreferences());
    }

    async handleGeneration() {
        this.setLoadingState(true);
        try {
            const prompt = this.promptInput.innerText.trim();
            const agent = this.getSelectedAgent();
            
            if (!this.validatePrompt(prompt)) return;
            
            const response = await agent.processPrompt(prompt);
            this.displayResponse(response);
            this.saveHistory(prompt, response);
        } catch (error) {
            this.showToast(error.message, 'error');
        } finally {
            this.setLoadingState(false);
        }
    }

    getSelectedAgent() {
        const agentKey = this.agentSelect.value;
        return new AGENTS[agentKey]();
    }

    validatePrompt(prompt) {
        if (!prompt) {
            this.showToast('Please enter a prompt', 'error');
            return false;
        }
        return true;
    }

    async displayResponse(response) {
        const responseElement = document.createElement('div');
        responseElement.className = 'response-card';
        this.responseContainer.prepend(responseElement);
        
        // Simulate typing effect
        for (let i = 0; i < response.length; i++) {
            responseElement.innerHTML = response.substring(0, i+1);
            await new Promise(resolve => setTimeout(resolve, 20));
        }
    }

    // Utility methods
    debounce(func, delay) {
        let timeout;
        return (...args) => {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), delay);
        };
    }

    showToast(message, type = 'info') {
        this.toast.textContent = message;
        this.toast.className = `toast ${type}`;
        this.toast.classList.add('show');
        setTimeout(() => this.toast.classList.remove('show'), 3000);
    }

    setLoadingState(isLoading) {
        this.generateBtn.disabled = isLoading;
        this.promptInput.contentEditable = !isLoading;
        this.generateBtn.querySelector('.loader').style.display = 
            isLoading ? 'block' : 'none';
    }

    saveHistory(prompt, response) {
        this.history.push({ prompt, response, timestamp: new Date() });
        localStorage.setItem('conversationHistory', JSON.stringify(this.history));
    }

    loadPreferences() {
        const savedAgent = localStorage.getItem('preferredAgent');
        if (savedAgent) this.agentSelect.value = savedAgent;
    }

    savePreferences() {
        localStorage.setItem('preferredAgent', this.agentSelect.value);
    }
}

// Initialize application
new AIInterface();